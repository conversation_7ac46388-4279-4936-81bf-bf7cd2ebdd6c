import { useState, useCallback, useMemo, useEffect } from "react";

import { useLoaderData, useNavigation, useActionData, useSubmit } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  DataTable,
  Text,
  Badge,
  Spinner,
  EmptyState,
  Filters,
  ChoiceList,
  Tabs,
  Box,
  InlineStack,
  BlockStack,
  Modal,
  Button,
  Checkbox,
  Banner
} from "@shopify/polaris";

import { InventoryCalculationService } from "../services/inventory/InventoryCalculationService.js";

export const action = async ({ request }) => {
  // Import server-only functions inside the action
  const { authenticateMultistore } = await import("../shopify.multistore.server");

  // Use multistore authentication
  const result = await authenticateMultistore(request);

  // If result is a Response (redirect), throw it to let Remix handle it
  if (result instanceof Response) {
    throw result;
  }

  const { session } = result;

  // Import server-only functions to check if this is an admin shop
  const { isAdminShop } = await import("../lib/config/index.js");
  const isAdmin = isAdminShop(session.shop);

  // Only allow admin shops to access this action
  if (!isAdmin) {
    return Response.json({ error: "Access denied. Admin privileges required." }, { status: 403 });
  }

  try {
    const formData = await request.formData();
    const action = formData.get("action");

    const inventoryService = new InventoryCalculationService();

    if (action === "markSelectedAsOrdered") {
      const selectionsJson = formData.get("selections");
      const selections = JSON.parse(selectionsJson);

      const results = await inventoryService.markInventoryAsOrdered(selections);

      return Response.json({
        success: true,
        message: `Successfully marked ${results.deleted} inventory requirements as ordered. Deleted ${results.transactionsDeleted} transactions.`,
        results,
        action: "markSelectedAsOrdered"
      });

    } else if (action === "markAllAsOrdered") {
      const category = formData.get("category");

      const results = await inventoryService.markAllInventoryAsOrdered(category);

      return Response.json({
        success: true,
        message: `Successfully reset all ${category || 'inventory'} requirements. Deleted ${results.deleted} requirements and ${results.transactionsDeleted} transactions.`,
        results,
        action: "markAllAsOrdered"
      });

    } else {
      return Response.json({ error: "Invalid action" }, { status: 400 });
    }

  } catch (error) {
    console.error("Error in inventory action:", error);

    return Response.json({
      success: false,
      error: error.message || "An error occurred while processing the request"
    }, { status: 500 });
  }
};

export const loader = async ({ request }) => {
  // Import server-only functions inside the loader
  const { authenticateMultistore } = await import("../shopify.multistore.server");

  // Use multistore authentication
  const result = await authenticateMultistore(request);

  // If result is a Response (redirect), throw it to let Remix handle it
  if (result instanceof Response) {
    throw result;
  }

  const { session } = result;

  // Import server-only functions to check if this is an admin shop
  const { isAdminShop } = await import("../lib/config/index.js");
  const isAdmin = isAdminShop(session.shop);

  // Only allow admin shops to access this page
  if (!isAdmin) {
    return new Response("Access denied. Admin privileges required.", { status: 403 });
  }

  try {

    const inventoryService = new InventoryCalculationService();

    // Get aggregated inventory requirements for all stores
    const inventoryData = await inventoryService.getAllInventoryRequirements({
      minQuantity: 0, // Include all items, even with 0 quantity
      includeTransactions: true, // Include transaction history for modal details
      transactionLimit: 50 // Limit transactions per requirement
    });

    console.log('Inventory data loaded:', {
      regular: inventoryData.regular?.length || 0,
      engraving: inventoryData.engraving?.length || 0,
      totalRegular: inventoryData.regular?.reduce((sum, item) => sum + item.requiredQuantity, 0) || 0
    });

    return Response.json({
      inventoryData,
      shop: session.shop,
      isAdmin,
      success: true
    });

  } catch (error) {
    console.error("Error loading inventory requirements:", error);

    return Response.json({
      inventoryData: { regular: [], engraving: [] },
      shop: session.shop,
      success: false,
      error: error.message || error.toString() || "Unknown error occurred"
    });
  }
};

export default function InventoryPage() {
  const { inventoryData, success, error } = useLoaderData();
  const actionData = useActionData();
  const submit = useSubmit();
  const navigation = useNavigation();
  const isLoading = navigation.state === "loading";
  const isSubmitting = navigation.state === "submitting";

  // Provide fallback for inventoryData
  const safeInventoryData = inventoryData || { regular: [], engraving: [] };

  // State for filtering and search
  const [selectedTab, setSelectedTab] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFilters, setSelectedFilters] = useState({
    garmentCode: [],
    color: [],
    size: []
  });

  // State for detail modal
  const [modalActive, setModalActive] = useState(false);
  const [modalData, setModalData] = useState(null);

  // State for selection and ordering
  const [selectedItems, setSelectedItems] = useState(new Set());
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmAction, setConfirmAction] = useState(null);
  const [notification, setNotification] = useState(null);

  // Handle clicking on quantity badges to show details
  const handleQuantityClick = useCallback((garmentCode, color, size, items) => {
    // Filter items to only those matching the specific color/size combination
    const matchingItems = items.filter(item =>
      item.color === color && item.size === size
    );

    if (matchingItems.length > 0) {
      // Get all transactions from matching items for detailed view
      const allTransactions = matchingItems.flatMap(item => item.transactions || []);

      // Group transactions by unique SKU/variant combinations
      const transactionGroups = new Map();
      allTransactions.forEach(transaction => {
        const key = `${transaction.sku || 'Unknown'}-${transaction.variantTitle || 'Unknown'}`;
        if (transactionGroups.has(key)) {
          const existing = transactionGroups.get(key);
          existing.totalQuantity += Math.abs(transaction.quantityChange);
        } else {
          transactionGroups.set(key, {
            sku: transaction.sku || 'Unknown SKU',
            variantTitle: transaction.variantTitle || 'Unknown Variant',
            totalQuantity: Math.abs(transaction.quantityChange)
          });
        }
      });

      setModalData({
        garmentCode,
        color,
        size,
        items: Array.from(transactionGroups.values()),
        totalQuantity: matchingItems.reduce((sum, item) => sum + item.requiredQuantity, 0)
      });
      setModalActive(true);
    }
  }, []);

  // Handle selection of individual items
  const handleItemSelection = useCallback((garmentCode, color, size, category, checked) => {
    const itemKey = `${garmentCode}|${color}|${size}|${category}`;
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(itemKey);
      } else {
        newSet.delete(itemKey);
      }
      return newSet;
    });
  }, []);

  // Tab configuration
  const tabs = [
    {
      id: "regular",
      content: "Regular Items",
      accessibilityLabel: "Regular inventory items",
      panelID: "regular-items-panel"
    },
    {
      id: "engraving",
      content: "Engraving Items",
      accessibilityLabel: "Engraving inventory items",
      panelID: "engraving-items-panel"
    }
  ];

  // Get current data based on selected tab
  const currentData = selectedTab === 0 ? safeInventoryData.regular : safeInventoryData.engraving;
  const currentCategory = selectedTab === 0 ? "regular" : "engraving";

  // Handle ordering actions
  const handleMarkSelectedAsOrdered = useCallback(() => {
    if (selectedItems.size === 0) {
      setNotification({
        status: "critical",
        message: "Please select items to mark as ordered"
      });
      return;
    }

    setConfirmAction({
      type: "markSelected",
      message: `Mark ${selectedItems.size} selected items as ordered? This action is irreversible and will permanently reset the selected inventory counts.`
    });
    setShowConfirmModal(true);
  }, [selectedItems]);

  const handleMarkAllAsOrdered = useCallback(() => {
    setConfirmAction({
      type: "markAll",
      message: `Mark all ${currentCategory} inventory requirements as ordered? This action is irreversible and will permanently reset all inventory counts for this category.`
    });
    setShowConfirmModal(true);
  }, [currentCategory]);

  const handleConfirmAction = useCallback(() => {
    if (!confirmAction) return;

    const formData = new FormData();

    if (confirmAction.type === "markSelected") {
      // Convert selected items to array of objects
      const selections = Array.from(selectedItems).map(itemKey => {
        const [garmentCode, color, size, category] = itemKey.split('|');
        return { garmentCode, color, size, category };
      });

      formData.append("action", "markSelectedAsOrdered");
      formData.append("selections", JSON.stringify(selections));
    } else if (confirmAction.type === "markAll") {
      formData.append("action", "markAllAsOrdered");
      formData.append("category", currentCategory);
    }

    submit(formData, { method: "post" });
  }, [confirmAction, selectedItems, currentCategory, submit]);

  // Handle action data effects
  useEffect(() => {
    if (actionData?.success) {
      setNotification({
        status: "success",
        message: actionData.message
      });

      // Clear selections and close modal
      setSelectedItems(new Set());
      setShowConfirmModal(false);
      setConfirmAction(null);

      // Auto-hide notification after 5 seconds
      setTimeout(() => setNotification(null), 5000);

      // Reload the page to get fresh data
      window.location.reload();
    } else if (actionData?.error) {
      setNotification({
        status: "critical",
        message: actionData.error
      });

      // Auto-hide notification after 5 seconds
      setTimeout(() => setNotification(null), 5000);
    }
  }, [actionData]);

  // Extract unique filter options from current data
  const filterOptions = useMemo(() => {
    // Define the standard size order
    const sizeOrder = ['XS', 'S', 'M', 'L', 'XL', '2XL', '3XL'];

    const garmentCodes = [...new Set(currentData.map(item => item.garmentCode))].sort();
    const colors = [...new Set(currentData.map(item => item.color))].sort();
    const availableSizes = [...new Set(currentData.map(item => item.size))];

    // Sort sizes according to the standard order, keeping any non-standard sizes at the end
    const sizes = sizeOrder.filter(size => availableSizes.includes(size))
      .concat(availableSizes.filter(size => !sizeOrder.includes(size)).sort());

    return {
      garmentCode: garmentCodes.map(code => ({ label: code, value: code })),
      color: colors.map(color => ({ label: color, value: color })),
      size: sizes.map(size => ({ label: size, value: size }))
    };
  }, [currentData]);

  // Filter and search data
  const filteredData = useMemo(() => {
    let filtered = currentData;

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item =>
        item.garmentCode.toLowerCase().includes(query) ||
        item.color.toLowerCase().includes(query) ||
        item.size.toLowerCase().includes(query)
      );
    }

    // Apply selected filters
    if (selectedFilters.garmentCode.length > 0) {
      filtered = filtered.filter(item => selectedFilters.garmentCode.includes(item.garmentCode));
    }
    if (selectedFilters.color.length > 0) {
      filtered = filtered.filter(item => selectedFilters.color.includes(item.color));
    }
    if (selectedFilters.size.length > 0) {
      filtered = filtered.filter(item => selectedFilters.size.includes(item.size));
    }

    return filtered;
  }, [currentData, searchQuery, selectedFilters]);

  // Handle select all for current filtered data
  const handleSelectAll = useCallback((checked) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      filteredData.forEach(item => {
        const itemKey = `${item.garmentCode}|${item.color}|${item.size}|${item.category}`;
        if (checked && item.requiredQuantity > 0) {
          newSet.add(itemKey);
        } else {
          newSet.delete(itemKey);
        }
      });
      return newSet;
    });
  }, [filteredData]);

  // Group filtered data by garment code
  const groupedData = useMemo(() => {
    const groups = {};

    filteredData.forEach(item => {
      if (!groups[item.garmentCode]) {
        groups[item.garmentCode] = [];
      }
      groups[item.garmentCode].push(item);
    });

    // Sort garment codes
    const sortedGroups = {};
    Object.keys(groups).sort().forEach(garmentCode => {
      sortedGroups[garmentCode] = groups[garmentCode];
    });

    return sortedGroups;
  }, [filteredData]);

  // Calculate totals
  const totals = useMemo(() => {
    const totalItems = filteredData.length;
    const totalQuantity = filteredData.reduce((sum, item) => sum + item.requiredQuantity, 0);
    const itemsWithRequirements = filteredData.filter(item => item.requiredQuantity > 0).length;
    const totalGarmentCodes = Object.keys(groupedData).length;

    return { totalItems, totalQuantity, itemsWithRequirements, totalGarmentCodes };
  }, [filteredData, groupedData]);

  // Helper function to create a table for a specific garment code
  const createGarmentTable = useCallback((garmentCode, items) => {
    // Define the standard size order
    const standardSizes = ['S', 'M', 'L', 'XL', '2XL', '3XL'];

    // Get unique colors and sizes for this garment
    const colors = [...new Set(items.map(item => item.color))].sort();
    const availableSizes = [...new Set(items.map(item => item.size))];

    // Check if this garment only has OSFA (One Size Fits All) items
    const isOSFAOnly = availableSizes.length === 1 && availableSizes[0] === 'OSFA';

    let sizes;

    if (isOSFAOnly) {
      // For OSFA items, only show the OSFA column
      sizes = ['OSFA'];
    } else {
      // For regular items, use the standard size order
      // Check if XS has any quantities for this garment
      const hasXSWithQuantity = items.some(item => item.size === 'XS' && item.requiredQuantity > 0);

      // Build the sizes array: include XS only if it has quantities, always include standard sizes, then any non-standard sizes
      sizes = [];

      // Add XS only if it has quantities
      if (hasXSWithQuantity) {
        sizes.push('XS');
      }

      // Always add standard sizes
      sizes.push(...standardSizes);

      // Add any non-standard sizes that exist (excluding OSFA which is handled separately)
      const nonStandardSizes = availableSizes.filter(size =>
        !['XS', ...standardSizes, 'OSFA'].includes(size)
      ).sort();
      sizes.push(...nonStandardSizes);
    }

    // Create a matrix of color x size with quantities
    const matrix = {};
    items.forEach(item => {
      if (!matrix[item.color]) {
        matrix[item.color] = {};
      }
      matrix[item.color][item.size] = item.requiredQuantity;
    });

    // Create table headings: Select column + Color column + size columns
    const headings = ["Select", "Color", ...sizes];

    // Create table rows
    const rows = colors.map(color => {
      const row = [];

      // Add select all checkbox for this color row
      const colorItems = items.filter(item => item.color === color && item.requiredQuantity > 0);
      const colorItemKeys = colorItems.map(item => `${item.garmentCode}|${item.color}|${item.size}|${item.category}`);
      const allColorItemsSelected = colorItemKeys.length > 0 && colorItemKeys.every(key => selectedItems.has(key));
      const someColorItemsSelected = colorItemKeys.some(key => selectedItems.has(key));

      row.push(
        <Checkbox
          key={`select-${color}`}
          checked={allColorItemsSelected}
          indeterminate={someColorItemsSelected && !allColorItemsSelected}
          onChange={(checked) => {
            setSelectedItems(prev => {
              const newSet = new Set(prev);
              colorItemKeys.forEach(key => {
                if (checked) {
                  newSet.add(key);
                } else {
                  newSet.delete(key);
                }
              });
              return newSet;
            });
          }}
          disabled={colorItems.length === 0}
        />
      );

      // Add color name
      row.push(color);

      // Add size columns with quantity badges and individual checkboxes
      sizes.forEach(size => {
        const quantity = matrix[color]?.[size] || 0;
        const item = items.find(item => item.color === color && item.size === size);
        const itemKey = item ? `${item.garmentCode}|${item.color}|${item.size}|${item.category}` : null;
        const isSelected = itemKey ? selectedItems.has(itemKey) : false;

        row.push(
          <InlineStack key={`${color}-${size}`} gap="200" align="center">
            {quantity > 0 && (
              <Checkbox
                checked={isSelected}
                onChange={(checked) => {
                  if (itemKey) {
                    handleItemSelection(item.garmentCode, item.color, item.size, item.category, checked);
                  }
                }}
              />
            )}
            {quantity > 0 ? (
              <Button
                variant="plain"
                onClick={() => handleQuantityClick(garmentCode, color, size, items)}
              >
                <Badge tone="attention">
                  {quantity}
                </Badge>
              </Button>
            ) : (
              <div style={{ width: '100%', height: '32px' }}></div>
            )}
          </InlineStack>
        );
      });
      return row;
    });

    // Calculate total for this garment
    const totalQuantity = items.reduce((sum, item) => sum + item.requiredQuantity, 0);
    const itemsWithRequirements = items.filter(item => item.requiredQuantity > 0).length;

    return {
      headings,
      rows,
      totalQuantity,
      itemsWithRequirements,
      totalItems: items.length
    };
  }, [handleQuantityClick, handleItemSelection, selectedItems]);

  // Filter handlers
  const handleFiltersChange = useCallback((filters) => {
    setSelectedFilters(filters);
  }, []);

  const handleSearchChange = useCallback((value) => {
    setSearchQuery(value);
  }, []);

  const handleFiltersClearAll = useCallback(() => {
    setSelectedFilters({
      garmentCode: [],
      color: [],
      size: []
    });
    setSearchQuery("");
  }, []);

  // Filter components
  const filters = [
    {
      key: "garmentCode",
      label: "Garment Code",
      filter: (
        <ChoiceList
          title="Garment Code"
          titleHidden
          choices={filterOptions.garmentCode}
          selected={selectedFilters.garmentCode}
          onChange={(value) => handleFiltersChange({ ...selectedFilters, garmentCode: value })}
          allowMultiple
        />
      ),
      shortcut: true
    },
    {
      key: "color",
      label: "Color",
      filter: (
        <ChoiceList
          title="Color"
          titleHidden
          choices={filterOptions.color}
          selected={selectedFilters.color}
          onChange={(value) => handleFiltersChange({ ...selectedFilters, color: value })}
          allowMultiple
        />
      ),
      shortcut: true
    },
    {
      key: "size",
      label: "Size",
      filter: (
        <ChoiceList
          title="Size"
          titleHidden
          choices={filterOptions.size}
          selected={selectedFilters.size}
          onChange={(value) => handleFiltersChange({ ...selectedFilters, size: value })}
          allowMultiple
        />
      ),
      shortcut: true
    }
  ];

  const appliedFilters = [];
  if (selectedFilters.garmentCode.length > 0) {
    appliedFilters.push({
      key: "garmentCode",
      label: `Garment Code: ${selectedFilters.garmentCode.join(", ")}`,
      onRemove: () => handleFiltersChange({ ...selectedFilters, garmentCode: [] })
    });
  }
  if (selectedFilters.color.length > 0) {
    appliedFilters.push({
      key: "color",
      label: `Color: ${selectedFilters.color.join(", ")}`,
      onRemove: () => handleFiltersChange({ ...selectedFilters, color: [] })
    });
  }
  if (selectedFilters.size.length > 0) {
    appliedFilters.push({
      key: "size",
      label: `Size: ${selectedFilters.size.join(", ")}`,
      onRemove: () => handleFiltersChange({ ...selectedFilters, size: [] })
    });
  }

  if (!success) {
    return (
      <Page title="Inventory Requirements">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <Text variant="headingMd" as="h2">Error Loading Inventory Data</Text>
                <Text>{error || "An unknown error occurred while loading inventory requirements."}</Text>
              </BlockStack>
            </Card>
          </Layout.Section>
        </Layout>
      </Page>
    );
  }

  return (
    <>
      <style>{`
        .inventory-table-container {
          overflow-x: visible;
        }
        .inventory-table-container .Polaris-DataTable {
          table-layout: fixed;
          width: 100%;
        }
        .inventory-table-container .Polaris-DataTable__Cell:nth-child(1),
        .inventory-table-container .Polaris-DataTable__Heading:nth-child(1) {
          width: 50px;
          min-width: 50px;
          max-width: 50px;
        }
        .inventory-table-container .Polaris-DataTable__Cell:nth-child(2),
        .inventory-table-container .Polaris-DataTable__Heading:nth-child(2) {
          width: 140px;
          min-width: 140px;
          max-width: 140px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .inventory-table-container .Polaris-DataTable__Cell:nth-child(n+3),
        .inventory-table-container .Polaris-DataTable__Heading:nth-child(n+3) {
          width: 80px;
          min-width: 80px;
          max-width: 80px;
          text-align: center;
        }
        .inventory-table-container .Polaris-DataTable__Cell:nth-child(n+3) > div {
          justify-content: center;
        }
        .inventory-table-container .Polaris-DataTable__ScrollContainer {
          overflow-x: visible;
        }
      `}</style>
      <Page
        title="Inventory Requirements"
        subtitle="Aggregated inventory needs across all orders"
      >
        <Layout>
        <Layout.Section>
          <Card>
            <Tabs tabs={tabs} selected={selectedTab} onSelect={setSelectedTab}>
              <Box padding="400">
                {isLoading ? (
                  <div style={{ textAlign: "center", padding: "2rem" }}>
                    <Spinner size="large" />
                    <Text>Loading inventory requirements...</Text>
                  </div>
                ) : (
                  <BlockStack gap="400">
                    {/* Summary Stats */}
                    <InlineStack gap="400" align="space-between">
                      <Text variant="headingMd" as="h3">
                        {currentCategory === "regular" ? "Regular Items" : "Engraving Items"}
                      </Text>
                      <InlineStack gap="200">
                        <Badge tone="info">{totals.totalGarmentCodes} garment codes</Badge>
                        <Badge tone="attention">{totals.itemsWithRequirements} need inventory</Badge>
                        <Badge tone="warning">{totals.totalQuantity} total quantity needed</Badge>
                      </InlineStack>
                    </InlineStack>

                    {/* Filters */}
                    <Filters
                      queryValue={searchQuery}
                      queryPlaceholder="Search garment codes, colors, or sizes..."
                      filters={filters}
                      appliedFilters={appliedFilters}
                      onQueryChange={handleSearchChange}
                      onQueryClear={() => setSearchQuery("")}
                      onClearAll={handleFiltersClearAll}
                    />

                    {/* Notification Banner */}
                    {notification && (
                      <Banner
                        title={notification.status === "success" ? "Success" : "Error"}
                        tone={notification.status}
                        onDismiss={() => setNotification(null)}
                      >
                        <p>{notification.message}</p>
                      </Banner>
                    )}

                    {/* Action Buttons */}
                    <InlineStack gap="400" align="space-between">
                      <InlineStack gap="200">
                        <Button
                          onClick={handleMarkSelectedAsOrdered}
                          disabled={selectedItems.size === 0 || isSubmitting}
                          loading={isSubmitting && confirmAction?.type === "markSelected"}
                          tone="critical"
                        >
                          Mark Selected as Ordered ({selectedItems.size})
                        </Button>
                        <Button
                          onClick={() => handleSelectAll(true)}
                          disabled={filteredData.filter(item => item.requiredQuantity > 0).length === 0}
                        >
                          Select All
                        </Button>
                        <Button
                          onClick={() => handleSelectAll(false)}
                          disabled={selectedItems.size === 0}
                        >
                          Clear Selection
                        </Button>
                      </InlineStack>
                      <Button
                        onClick={handleMarkAllAsOrdered}
                        disabled={totals.totalQuantity === 0 || isSubmitting}
                        loading={isSubmitting && confirmAction?.type === "markAll"}
                        tone="critical"
                      >
                        Mark All as Ordered
                      </Button>
                    </InlineStack>

                    {/* Grouped Tables by Garment Code */}
                    {Object.keys(groupedData).length === 0 ? (
                      <EmptyState
                        heading={searchQuery || appliedFilters.length > 0 ? "No items match your filters" : "No inventory requirements found"}
                        image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
                      >
                        {searchQuery || appliedFilters.length > 0 ? (
                          <p>Try adjusting your search or filters to find what you're looking for.</p>
                        ) : (
                          <p>Inventory requirements will appear here once orders are processed.</p>
                        )}
                      </EmptyState>
                    ) : (
                      <BlockStack gap="600">
                        {Object.entries(groupedData).map(([garmentCode, items]) => {
                          const tableData = createGarmentTable(garmentCode, items);

                          return (
                            <Card key={garmentCode}>
                              <BlockStack gap="400">
                                <InlineStack gap="400" align="space-between">
                                  <Text variant="headingMd" as="h4">
                                    {garmentCode}
                                  </Text>
                                  <InlineStack gap="200">
                                    <Badge tone="info">{tableData.totalItems} items</Badge>
                                    <Badge tone="attention">{tableData.itemsWithRequirements} need inventory</Badge>
                                    <Badge tone="warning">{tableData.totalQuantity} total needed</Badge>
                                  </InlineStack>
                                </InlineStack>

                                <div className="inventory-table-container">
                                  <DataTable
                                    columnContentTypes={["text", ...Array(tableData.headings.length - 1).fill("text")]}
                                    headings={tableData.headings}
                                    rows={tableData.rows}
                                  />
                                </div>
                              </BlockStack>
                            </Card>
                          );
                        })}
                      </BlockStack>
                    )}
                  </BlockStack>
                )}
              </Box>
            </Tabs>
          </Card>
        </Layout.Section>
      </Layout>

      {/* Detail Modal */}
      <Modal
        open={modalActive}
        onClose={() => setModalActive(false)}
        title={modalData ? `${modalData.garmentCode} - ${modalData.color} / ${modalData.size}` : "Details"}
        primaryAction={{
          content: 'Close',
          onAction: () => setModalActive(false),
        }}
      >
        <Modal.Section>
          {modalData && (
            <BlockStack gap="400">
              <InlineStack gap="400" align="space-between">
                <Text variant="headingMd" as="h3">
                  Inventory Details
                </Text>
                <Badge tone="warning">
                  {modalData.totalQuantity} total needed
                </Badge>
              </InlineStack>

              <DataTable
                columnContentTypes={["text", "text", "numeric"]}
                headings={["SKU", "Variant", "Quantity"]}
                rows={modalData.items.map(item => [
                  item.sku,
                  item.variantTitle,
                  item.totalQuantity
                ])}
              />
            </BlockStack>
          )}
        </Modal.Section>
      </Modal>

      {/* Confirmation Modal */}
      <Modal
        open={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        title="Confirm Action"
        primaryAction={{
          content: 'Confirm',
          onAction: handleConfirmAction,
          loading: isSubmitting,
          destructive: true
        }}
        secondaryActions={[
          {
            content: 'Cancel',
            onAction: () => setShowConfirmModal(false)
          }
        ]}
      >
        <Modal.Section>
          <BlockStack gap="400">
            <Text variant="headingMd" as="h3" tone="critical">
              Warning: This action is irreversible
            </Text>
            <Text>
              {confirmAction?.message}
            </Text>
            <Text tone="subdued">
              This will permanently remove the selected inventory requirements and all associated transaction history from the database.
            </Text>
          </BlockStack>
        </Modal.Section>
      </Modal>
      </Page>
    </>
  );
}
