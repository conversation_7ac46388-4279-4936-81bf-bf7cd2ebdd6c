
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Card,
  InlineStack,
  Layout,
  Page,
  Select,
  Spinner,
  Text,
  Banner,
  But<PERSON>,
} from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";

import { getInvoiceBalances } from "../models/InvoiceBalance.server";
// Utility functions
import calculateTotalBalance from "../utils/balance"; // Used in the useMemo calculation
import { useLoaderData, useFetcher } from "@remix-run/react";
import { Total } from "../components/Total"
import { getUnprocessables } from "../models/Unprocessable.server";
import ErrorList from "../components/ErrorList"
import { useCallback, useState, useMemo, useEffect } from "react";
import {
  getSetupFlag,
  checkAndMarkSetupInProgress,
  markSetupCompleted,
  resetProcessingFlag
} from "../models/IsSetup.server";
import { groupBalanceItems } from "../utils/productGroups";
import { container } from "../lib/container/ServiceContainer.server.js";


import { ShippingCostsTotal } from "../components/ShippingCostsTotal";
import { getShippingCostsByMonth } from "../models/ShippingCost.server";

export async function action({ request }) {
  // Import server-only functions inside the action
  const { authenticateMultistore } = await import("../shopify.multistore.server");

  // Use multistore authentication
  const result = await authenticateMultistore(request);

  // If result is a Response (redirect), throw it to let Remix handle it
  if (result instanceof Response) {
    throw result;
  }

  const { session } = result;

  try {
    const formData = await request.formData();
    const action = formData.get("action");

    if (action === "retry-unprocessables") {
      // Get transaction processing service from container
      const transactionProcessingService = await container.resolve('transactionProcessingService');

      // Attempt to reprocess unprocessable items
      const retryResult = await transactionProcessingService.processUnprocessableItems(session.shop);

      return Response.json({
        success: true,
        retryResults: {
          success: retryResult.successful,
          failed: retryResult.failed
        }
      });
    }

    return Response.json({ success: false, error: "Invalid action" }, { status: 400 });

  } catch (error) {
    console.error(`Error in retry action:`, error);
    return Response.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

export async function loader({ request }) {
  // Import server-only functions inside the loader
  const { authenticateMultistore } = await import("../shopify.multistore.server");

  // Use multistore authentication
  const result = await authenticateMultistore(request);

  // If result is a Response (redirect), throw it to let Remix handle it
  if (result instanceof Response) {
    throw result;
  }

  const { admin, session } = result;

  try {
    // Check if the shop is already set up
    const setupFlag = await getSetupFlag(session.shop);

    // If the shop is already set up, fetch the data directly
    if (setupFlag && setupFlag.isSetup) {
      return await fetchShopData(session);
    }

    // If we get here, the shop is not set up yet

    // Atomically check if setup is already in progress and mark it as such if not
    const setupStatus = await checkAndMarkSetupInProgress(session.shop);

    if (setupStatus.isSetup) {
      return await fetchShopData(session);
    }

    if (!setupStatus.canProceed) {
      return {
        balances: null,
        unprocessables: null,
        isLoading: true,
        processingMessage: "Another process is already setting up your shop. Please wait..."
      };
    }

    // If we get here, we've successfully marked this shop as processing and can proceed

    // Get setup service from container
    const setupService = container.resolve('setupService');

    // Check if there's a bulk operation running
    let bulkData = await setupService.checkBulkRunning(admin);

    // If there's a completed bulk operation, process it
    if (bulkData.data.currentBulkOperation && bulkData.data.currentBulkOperation.status === "COMPLETED") {
      try {
        // Process the bulk operation data
        const processResult = await processBulkOperation(bulkData.data.currentBulkOperation, session, admin);

        // Check if there was no data to process (null URL)
        if (processResult.noData) {
          // Mark setup as completed even if there was no data
          await markSetupCompleted(session.shop);

          // Return empty data but not in loading state
          // This will show a normal invoice page with 0.00 balance
          return {
            balances: [],
            unprocessables: [],
            shippingCosts: [],
            retryResults: { success: 0, failed: 0 },
            noData: true
          };
        }

        // Mark setup as completed
        await markSetupCompleted(session.shop);

        // Fetch the shop data
        return await fetchShopData(session);
      } catch (error) {
        console.error(`Error processing bulk operation:`, error);

        // Reset the processing flag
        await resetProcessingFlag(session.shop);

        return {
          balances: null,
          unprocessables: null,
          isLoading: true,
          error: `Error processing data: ${error.message}`
        };
      }
    }

    // If there's a running bulk operation, return a loading state
    if (bulkData.data.currentBulkOperation) {
      // Reset the processing flag so other requests can check the status
      await resetProcessingFlag(session.shop);

      return {
        balances: null,
        unprocessables: null,
        isLoading: true,
        processingMessage: `Bulk operation in progress (${bulkData.data.currentBulkOperation.status}). Please wait...`
      };
    }

    // If there's no bulk operation, start one

    try {
      // Use the setupService to initiate bulk processing (which waits for completion)
      const bulkResults = await setupService.initiateBulkOrderProcessing(session.shop);

      // Check if there was no data to process
      if (bulkResults.processedCount === 0 && bulkResults.errorCount === 0) {
        // Mark setup as completed even if there was no data
        await markSetupCompleted(session.shop);

        // Return empty data but not in loading state
        return {
          balances: [],
          unprocessables: [],
          shippingCosts: [],
          retryResults: { success: 0, failed: 0 },
          noData: true
        };
      }

      // Mark setup as completed
      await markSetupCompleted(session.shop);

      // Fetch the shop data
      return await fetchShopData(session);
    } catch (error) {
      console.error(`Error starting bulk operation:`, error);

      // Reset the processing flag
      await resetProcessingFlag(session.shop);

      // If the error is related to no data, handle it gracefully
      if (error.message && (
          error.message.includes("null URL") ||
          error.message.includes("no data") ||
          error.message.includes("empty result"))) {

        // Mark setup as completed even if there was no data
        await markSetupCompleted(session.shop);

        // Return empty data but not in loading state
        return {
          balances: [],
          unprocessables: [],
          shippingCosts: [],
          retryResults: { success: 0, failed: 0 },
          noData: true
        };
      }

      return {
        balances: null,
        unprocessables: null,
        isLoading: true,
        error: `Error starting bulk operation: ${error.message}`
      };
    }
  } catch (error) {
    console.error(`Error:`, error);

    // Return a loading state with error
    return {
      balances: null,
      unprocessables: null,
      isLoading: true,
      error: error.message
    };
  }
}

/**
 * Process a bulk operation
 */
async function processBulkOperation(bulkOperation, session, admin) {
  // Check if the URL is null (which can happen for shops with no historical data)
  if (!bulkOperation.url) {
    return {
      processedCount: 0,
      skippedCount: 0,
      errorCount: 0,
      unprocessableItems: [],
      skippedFulfillmentItems: [],
      noData: true
    };
  }

  // Process the bulk operation data
  // Ensure admin has the shop information
  const adminWithShop = {
    ...admin,
    session: {
      ...admin.session,
      shop: session.shop
    },
    shop: session.shop
  };



  // Get setup service from container
  const setupService = await container.resolve('setupService');

  const populateResults = await setupService.populateFromBulk(bulkOperation.url, session, adminWithShop);

  return populateResults;
}

/**
 * Fetch all shop data (balances, unprocessables, shipping costs)
 */
async function fetchShopData(session) {
  // Get current balances, shipping costs, and unprocessables
  let balances = await getInvoiceBalances(session.shop);
  let unprocessables = await getUnprocessables(session.shop);

  // Get current date for month/year
  const now = new Date();
  const currentMonth = now.getMonth();
  const currentYear = now.getFullYear();

  // Create an array to store all shipping costs
  let allShippingCosts = [];

  // Fetch shipping costs for each month up to the current month
  for (let month = 0; month <= currentMonth; month++) {
    const monthCosts = await getShippingCostsByMonth(session.shop, month, currentYear);
    allShippingCosts = [...allShippingCosts, ...monthCosts];
  }

  // No automatic retry on page load - only manual retry via button
  let retryResults = { success: 0, failed: 0 };

  return {
    balances,
    unprocessables,
    shippingCosts: allShippingCosts,
    retryResults: {
      success: retryResults.success,
      failed: retryResults.failed
    }
  };
}

export default function Index() {
  let { balances, unprocessables, shippingCosts, retryResults, partialData, isLoading, error, processingMessage, noData } = useLoaderData();
  const [showRetryBanner, setShowRetryBanner] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);
  const fetcher = useFetcher();



  // Auto-refresh the page if we're in a loading state
  useEffect(() => {
    let refreshTimer;
    if (isLoading) {
      refreshTimer = setTimeout(() => {
        window.location.reload();
      }, 5000); // Refresh every 5 seconds
    }

    return () => {
      if (refreshTimer) clearTimeout(refreshTimer);
    };
  }, [isLoading]);

  // Show retry banner only if items were successfully reprocessed
  useEffect(() => {
    if (retryResults && retryResults.success > 0) {
      setShowRetryBanner(true);

      // Auto-hide the banner after 10 seconds
      const timer = setTimeout(() => {
        setShowRetryBanner(false);
      }, 10000);

      return () => clearTimeout(timer);
    }
  }, [retryResults]);

  // Handle fetcher response for retry action
  useEffect(() => {
    if (fetcher.data && fetcher.state === "idle") {
      setIsRetrying(false);

      if (fetcher.data.success && fetcher.data.retryResults.success > 0) {
        setShowRetryBanner(true);

        // Auto-hide the banner after 10 seconds
        const timer = setTimeout(() => {
          setShowRetryBanner(false);
        }, 10000);

        // Reload the page to show updated data
        setTimeout(() => {
          window.location.reload();
        }, 1000);

        return () => clearTimeout(timer);
      }
    }
  }, [fetcher.data, fetcher.state]);

  // Handle retry button click
  const handleRetryUnprocessables = useCallback(() => {
    setIsRetrying(true);
    const formData = new FormData();
    formData.append("action", "retry-unprocessables");
    fetcher.submit(formData, { method: "post" });
  }, [fetcher]);
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth());

  // Filter balances by month and handle null balances
  const filteredBalances = useMemo(() => {
    const safeBalances = balances || [];
    if (!safeBalances.length) return [];

    // Ensure both are treated as numbers for comparison
    return safeBalances.filter(balance => Number(balance.month) === Number(selectedMonth));
  }, [balances, selectedMonth]);

  // Filter shipping costs by month
  const filteredShippingCosts = useMemo(() => {
    const safeShippingCosts = shippingCosts || [];
    if (!safeShippingCosts.length) {
      return [];
    }

    // Get the current year
    const currentYear = new Date().getFullYear();

    // Ensure both month and year are treated as numbers for comparison
    return safeShippingCosts.filter(cost =>
      Number(cost.month) === Number(selectedMonth) &&
      Number(cost.year) === currentYear
    );
  }, [shippingCosts, selectedMonth]);

  // Group balances by product type and calculate different totals
  const { displayData, totalBalance, costOfGoodsTotal, shippingAndFulfillmentTotal } = useMemo(() => {
    // Calculate shipping costs total from ShippingCost table
    const shippingCostTotal = filteredShippingCosts.reduce((sum, cost) => sum + Number(cost.totalAmount), 0);

    // Find fulfillment costs from InvoiceBalance table
    const fulfillmentCosts = filteredBalances.find(balance => balance.category === 'Fulfillment Costs');
    const fulfillmentCostTotal = fulfillmentCosts ? Number(fulfillmentCosts.balance) : 0;

    // Calculate cost of goods (all balances except fulfillment costs)
    const costOfGoodsBalances = filteredBalances.filter(balance => balance.category !== 'Fulfillment Costs');
    const costOfGoods = calculateTotalBalance(costOfGoodsBalances);

    // Calculate shipping and fulfillment total
    const shippingAndFulfillment = shippingCostTotal + fulfillmentCostTotal;

    // Calculate grand total
    const grandTotal = costOfGoods + shippingAndFulfillment;

    // Group balances (this will exclude Fulfillment Costs as we modified the groupBalanceItems function)
    const grouped = groupBalanceItems(filteredBalances);

    return {
      displayData: grouped,
      totalBalance: grandTotal,
      costOfGoodsTotal: costOfGoods,
      shippingAndFulfillmentTotal: shippingAndFulfillment
    };
  }, [filteredBalances, filteredShippingCosts]);

  const handleMonthChange = useCallback((value) => {
    setSelectedMonth(Number(value));
  }, []);

  // Show loading state only if we're actually loading and don't have noData flag
  if (isLoading && !noData && balances === null) {
    return (
      <Page>
        <TitleBar title="Processing Your Data" />
          <Layout>
            <Layout.Section>
              <BlockStack gap="500">
                <Card>
                  <BlockStack gap="300">
                    <Text as="h2" variant="headingMd">We're processing your order data</Text>
                    <Text as="p">This may take a few minutes for shops with a large number of orders.</Text>
                    <Text as="p">The page will automatically refresh when processing is complete.</Text>

                    {processingMessage && (
                      <Banner status="info">
                        <p><strong>Status:</strong> {processingMessage}</p>
                      </Banner>
                    )}

                    {error && (
                      <Banner status="critical">
                        <p><strong>Error:</strong> {error}</p>
                      </Banner>
                    )}

                    <div style={{ marginTop: "20px", textAlign: "center" }}>
                      <Spinner size="large" />
                    </div>
                  </BlockStack>
                </Card>
              </BlockStack>
            </Layout.Section>
          </Layout>
      </Page>
    )
  }

  // Initialize empty arrays if they're null
  balances = balances || [];
  unprocessables = unprocessables || [];
  shippingCosts = shippingCosts || [];


  const options = Array.from({length: new Date().getMonth() + 1}, (_, i) => ({
    label: new Date(0, i).toLocaleString("default", { month: "long" }),
    value: i,
  }));

  return (
    <Page>
      <TitleBar title="Estimated Production Costs by Month" />


      {showRetryBanner && (retryResults?.success > 0 || fetcher.data?.retryResults?.success > 0) ? (
        <div style={{ marginBottom: '20px' }}>
          <Banner
            title="Processing Complete"
            status="success"
            onDismiss={() => setShowRetryBanner(false)}
          >
            <p><strong>Successfully reprocessed {fetcher.data?.retryResults?.success || retryResults?.success || 0} previously unprocessable item{(fetcher.data?.retryResults?.success || retryResults?.success || 0) !== 1 ? 's' : ''}.</strong></p>
          </Banner>
        </div>
      ) : null}

      {partialData ? (
        <div style={{ marginBottom: '20px' }}>
          <Banner
            title="Data Processing in Progress"
            status="info"
          >
            <p><strong>We're still processing your order data. The information below may be incomplete.</strong></p>
            <p>Please check back in a few minutes for complete results.</p>
          </Banner>
        </div>
      ) : null}

      {noData ? (
        <div style={{ marginBottom: '20px' }}>
          <Banner
            title="No Order Data Found"
            status="info"
          >
            <p><strong>We couldn't find any order data for your shop.</strong></p>
            <p>This could be because your shop doesn't have any orders in the last 2 months.</p>
            <p>As new orders are placed, they will be processed and displayed here.</p>
          </Banner>
        </div>
      ) : null}
      <Layout className="full-width-layout">
        <Layout.Section variant="oneHalf*">
          <BlockStack gap="300">
          <Card>
            <BlockStack gap="500">
              <InlineStack align="space-between">
                <BlockStack gap="200">
                  <Text as="h2" variant="headingXl">Balance: {totalBalance.toLocaleString("en-US", { style: "currency", currency: "USD" })}</Text>
                  <div className="balance-card-subtotals">
                    <Text as="p" variant="headingLg" className="balance-card-subtotal" style={{textDecoration: 'none', borderBottom: 'none'}}>Cost of Goods: {costOfGoodsTotal.toLocaleString("en-US", { style: "currency", currency: "USD" })}</Text>
                    <Text as="p" variant="headingLg" className="balance-card-subtotal" style={{textDecoration: 'none', borderBottom: 'none'}}>Shipping & Fulfillment: {shippingAndFulfillmentTotal.toLocaleString("en-US", { style: "currency", currency: "USD" })}</Text>
                  </div>
                </BlockStack>
                <Select label="Select Month" options={options} onChange={handleMonthChange} value={selectedMonth} />
              </InlineStack>
            </BlockStack>
          </Card>

          <Card>
            <BlockStack gap="500">
              <Text as="h3" variant="headingLg">Estimated Costs by Category:</Text>
            </BlockStack>
          </Card>

          {displayData && displayData.length > 0 ? (
            displayData.map((balance) => (
              <Total
                key={balance.category}
                balance={balance}
                isGroup={true}
              />
            ))
          ) : (
            <Card>
              <Text as="p">No data available for the selected month.</Text>
            </Card>
          )}

          {/* Display shipping and fulfillment costs as a Total component */}
          {(() => {
            const fulfillmentCosts = filteredBalances.find(balance => balance.category === 'Fulfillment Costs');

            return (
              <ShippingCostsTotal
                shippingCosts={filteredShippingCosts}
                fulfillmentCosts={fulfillmentCosts}
              />
            );
          })()}
          </BlockStack>
        </Layout.Section>
        <Layout.Section variant="oneHalf">
          <BlockStack gap="500">
            <Card>
                <BlockStack gap="500">
                  <InlineStack align="space-between">
                    <BlockStack gap="200">
                      <Text as="h2" variant="headingMd">Unprocessed Items</Text>
                      {retryResults && retryResults.success > 0 && (
                        <Text as="p" color="success">
                          {retryResults.success} item{retryResults.success !== 1 ? 's' : ''} successfully processed
                        </Text>
                      )}
                      <Text as="p">The following items could not be processed, and were not included in the above totals</Text>
                    </BlockStack>
                    {unprocessables && unprocessables.length > 0 && (
                      <Button
                        onClick={handleRetryUnprocessables}
                        loading={isRetrying || fetcher.state === "submitting"}
                        disabled={isRetrying || fetcher.state === "submitting"}
                        variant="primary"
                      >
                        Retry Processing
                      </Button>
                    )}
                  </InlineStack>
                </BlockStack>
                <BlockStack gap="500">
                  {unprocessables && unprocessables.length > 0 ? (
                    <ErrorList errors={unprocessables} />
                  ) : (
                    <Text as="p">No unprocessed items for this period.</Text>
                  )}
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="300">
                  <Text as="p"><em>DISCLAIMER: The listed amounts are an estimate based off Shopify order data and are subject to change.</em></Text>
                  <Text as="p"><em>Shipping & fulfillment charges for items which have not yet shipped by the end of the month will be included in the following month's invoice.</em></Text>
                  <Text as="p"><em>If you are in your first month using this app, shipping costs may not reflect actual invoice, as manually tracked shipping charges may bleed over from the previous month</em></Text>
                </BlockStack>
              </Card>
          </BlockStack>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
