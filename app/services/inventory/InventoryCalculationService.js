/**
 * Inventory Calculation Service
 *
 * This service handles the core logic for calculating and tracking inventory requirements
 * based on orders and fulfillments. It processes order line items and updates inventory
 * requirements accordingly.
 */

import { SkuParsingService } from './SkuParsingService.js';
import { BusinessLogicError } from '../../lib/errors/AppError.js';

export class InventoryCalculationService {
  constructor(dependencies = {}) {
    this.dependencies = dependencies;
    this.prisma = null;
    this.skuParser = new SkuParsingService();
    this.initialized = false;
  }

  async initialize() {
    if (!this.initialized) {
      if (this.dependencies.prisma) {
        this.prisma = this.dependencies.prisma;
      } else {
        // Import the database directly
        const dbModule = await import('../../db.server.js');
        this.prisma = dbModule.default;
      }
      this.initialized = true;
    }
  }

  async ensureInitialized() {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  /**
   * Process an order and update inventory requirements
   * @param {object} order - Shopify order object
   * @param {string} transactionType - Type of transaction ('order_created', 'order_fulfilled', 'order_cancelled')
   * @returns {Promise<object>} - Processing results
   */
  async processOrderForInventory(order, transactionType = 'order_created') {
    try {
      const shop = this.extractShopFromOrder(order);
      const results = {
        processed: 0,
        skipped: 0,
        errors: [],
        inventoryUpdates: []
      };

      // Process each line item in the order
      for (const lineItem of order.line_items || []) {
        try {
          const result = await this.processLineItem(
            shop,
            order.id.toString(),
            lineItem,
            transactionType
          );

          if (result.skipped) {
            results.skipped++;
          } else {
            results.processed++;
            results.inventoryUpdates.push(result);
          }
        } catch (error) {
          results.errors.push({
            lineItemId: lineItem.id,
            sku: lineItem.sku,
            error: error.message
          });
        }
      }

      return results;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to process order for inventory: ${error.message}`,
        { orderId: order.id, transactionType }
      );
    }
  }

  /**
   * Process a single line item for inventory requirements
   * @param {string} shop - Shop identifier
   * @param {string} orderId - Order ID
   * @param {object} lineItem - Order line item
   * @param {string} transactionType - Transaction type
   * @returns {Promise<object>} - Processing result
   */
  async processLineItem(shop, orderId, lineItem, transactionType) {
    // Skip items with non-manual fulfillment service
    if (lineItem.fulfillment_service !== 'manual') {
      return { skipped: true, reason: 'Non-manual fulfillment service' };
    }

    // Parse SKU to extract inventory information
    // Use productTitle if variant_title is not available (common in bulk imports)
    const variantInfo = lineItem.variant_title || lineItem.title || '';
    const parseResult = this.skuParser.parseSkuForInventory(
      lineItem.sku,
      variantInfo,
      lineItem.product_type || ''
    );

    if (parseResult.shouldSkip) {
      return { skipped: true, reason: parseResult.reason };
    }

    // Calculate quantity change based on transaction type
    let quantityChange = 0;
    switch (transactionType) {
      case 'order_created':
        quantityChange = lineItem.quantity;
        break;
      case 'order_fulfilled':
        quantityChange = -lineItem.quantity; // Negative for fulfillments
        break;
      case 'order_cancelled':
        quantityChange = -lineItem.quantity; // Negative for cancellations
        break;
      default:
        throw new BusinessLogicError(`Unknown transaction type: ${transactionType}`);
    }

    // Update or create inventory requirement
    const inventoryRequirement = await this.updateInventoryRequirement(
      shop,
      parseResult.garmentCode,
      parseResult.color,
      parseResult.size,
      parseResult.category,
      quantityChange
    );

    // Create inventory transaction record
    const transaction = await this.createInventoryTransaction(
      inventoryRequirement.id,
      orderId,
      lineItem.id?.toString(),
      quantityChange,
      transactionType,
      lineItem
    );

    return {
      skipped: false,
      inventoryRequirement,
      transaction,
      parseResult
    };
  }

  /**
   * Update or create inventory requirement record
   * @param {string} shop - Shop identifier
   * @param {string} garmentCode - Garment code
   * @param {string} color - Color
   * @param {string} size - Size
   * @param {string} category - Category ('regular' or 'engraving')
   * @param {number} quantityChange - Change in required quantity
   * @returns {Promise<object>} - Inventory requirement record
   */
  async updateInventoryRequirement(shop, garmentCode, color, size, category, quantityChange) {
    try {
      await this.ensureInitialized();

      // Use upsert to create or update the inventory requirement
      const inventoryRequirement = await this.prisma.inventoryRequirement.upsert({
        where: {
          shop_garmentCode_color_size_category: {
            shop,
            garmentCode,
            color,
            size,
            category
          }
        },
        update: {
          requiredQuantity: {
            increment: quantityChange
          },
          updatedAt: new Date()
        },
        create: {
          shop,
          garmentCode,
          color,
          size,
          category,
          requiredQuantity: Math.max(0, quantityChange) // Don't allow negative initial quantities
        }
      });

      return inventoryRequirement;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to update inventory requirement: ${error.message}`,
        { shop, garmentCode, color, size, category, quantityChange }
      );
    }
  }

  /**
   * Create inventory transaction record
   * @param {number} inventoryRequirementId - Inventory requirement ID
   * @param {string} orderId - Order ID
   * @param {string} lineItemId - Line item ID
   * @param {number} quantityChange - Quantity change
   * @param {string} transactionType - Transaction type
   * @param {object} lineItem - Original line item data
   * @returns {Promise<object>} - Transaction record
   */
  async createInventoryTransaction(
    inventoryRequirementId,
    orderId,
    lineItemId,
    quantityChange,
    transactionType,
    lineItem
  ) {
    try {
      await this.ensureInitialized();

      // Extract a cleaner product name from the SKU
      const productTitle = this.extractProductNameFromSku(lineItem.sku);

      const transaction = await this.prisma.inventoryTransaction.create({
        data: {
          inventoryRequirementId,
          orderId,
          lineItemId,
          quantityChange,
          transactionType,
          sku: lineItem.sku,
          productTitle: productTitle,
          variantTitle: lineItem.variant_title || lineItem.title || '',
          metadata: JSON.stringify({
            originalQuantity: lineItem.quantity,
            price: lineItem.price,
            productId: lineItem.product_id,
            variantId: lineItem.variant_id,
            fulfillmentService: lineItem.fulfillment_service
          })
        }
      });

      return transaction;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to create inventory transaction: ${error.message}`,
        { inventoryRequirementId, orderId, lineItemId, transactionType }
      );
    }
  }

  /**
   * Extract a cleaner product name from SKU
   * @param {string} sku - Product SKU
   * @returns {string} - Cleaned product name
   */
  extractProductNameFromSku(sku) {
    if (!sku) return 'Unknown Product';

    // Remove prefix indicators like *, L-, *L-, etc.
    let cleanSku = sku.replace(/^\*?[A-Z]*-?/, '');

    // Split by hyphens and take meaningful parts
    const parts = cleanSku.split('-');

    // Remove the last part if it looks like a garment code (short alphanumeric)
    if (parts.length > 1) {
      const lastPart = parts[parts.length - 1];
      if (lastPart.length <= 6 && /^[A-Z0-9]+$/.test(lastPart)) {
        parts.pop();
      }
    }

    // Join remaining parts and clean up
    return parts
      .join(' ')
      .replace(/([A-Z])/g, ' $1') // Add spaces before capital letters
      .replace(/\s+/g, ' ') // Normalize spaces
      .trim()
      .replace(/\b\w/g, l => l.toUpperCase()) // Capitalize words
      || 'Unknown Product';
  }

  /**
   * Get inventory requirements for a shop
   * @param {string} shop - Shop identifier
   * @param {object} options - Query options
   * @returns {Promise<object>} - Inventory requirements grouped by category
   */
  async getInventoryRequirements(shop, options = {}) {
    try {
      await this.ensureInitialized();

      const where = { shop };

      // Add category filter if specified
      if (options.category) {
        where.category = options.category;
      }

      // Add minimum quantity filter
      if (options.minQuantity !== undefined) {
        where.requiredQuantity = { gte: options.minQuantity };
      }

      const requirements = await this.prisma.inventoryRequirement.findMany({
        where,
        orderBy: [
          { category: 'asc' },
          { garmentCode: 'asc' },
          { color: 'asc' },
          { size: 'asc' }
        ],
        include: {
          transactions: options.includeTransactions ? {
            orderBy: { createdAt: 'desc' },
            take: options.transactionLimit || 10
          } : false
        }
      });

      // Group by category
      const grouped = {
        regular: requirements.filter(r => r.category === 'regular'),
        engraving: requirements.filter(r => r.category === 'engraving')
      };

      return grouped;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get inventory requirements: ${error.message}`,
        { shop, options }
      );
    }
  }

  /**
   * Get inventory requirements for all shops (admin function)
   * @param {object} options - Query options
   * @returns {Promise<object>} - Inventory requirements grouped by category
   */
  async getAllInventoryRequirements(options = {}) {
    try {
      await this.ensureInitialized();

      const where = {};

      // Add category filter if specified
      if (options.category) {
        where.category = options.category;
      }

      // Add minimum quantity filter
      if (options.minQuantity !== undefined) {
        where.requiredQuantity = { gte: options.minQuantity };
      }

      const requirements = await this.prisma.inventoryRequirement.findMany({
        where,
        orderBy: [
          { shop: 'asc' },
          { category: 'asc' },
          { garmentCode: 'asc' },
          { color: 'asc' },
          { size: 'asc' }
        ],
        include: {
          transactions: options.includeTransactions ? {
            orderBy: { createdAt: 'desc' },
            take: options.transactionLimit || 10
          } : false
        }
      });

      // Aggregate requirements by garment code, color, size, and category
      const aggregated = this.aggregateInventoryRequirements(requirements);

      // Group by category
      const grouped = {
        regular: aggregated.filter(r => r.category === 'regular'),
        engraving: aggregated.filter(r => r.category === 'engraving')
      };

      return grouped;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get all inventory requirements: ${error.message}`,
        { options }
      );
    }
  }

  /**
   * Aggregate inventory requirements by garment code, color, size, and category
   * Sums quantities across all shops for the same item
   * @param {Array} requirements - Array of inventory requirements
   * @returns {Array} - Aggregated requirements
   */
  aggregateInventoryRequirements(requirements) {
    const aggregationMap = new Map();

    for (const req of requirements) {
      // Create a unique key for each garment/color/size/category combination
      const key = `${req.garmentCode}|${req.color}|${req.size}|${req.category}`;

      if (aggregationMap.has(key)) {
        // Add to existing aggregation
        const existing = aggregationMap.get(key);
        existing.requiredQuantity += req.requiredQuantity;
        existing.updatedAt = req.updatedAt > existing.updatedAt ? req.updatedAt : existing.updatedAt;

        // Merge transactions if they exist
        if (req.transactions && req.transactions.length > 0) {
          existing.transactions = existing.transactions || [];
          existing.transactions.push(...req.transactions);
        }
      } else {
        // Create new aggregation entry
        aggregationMap.set(key, {
          id: req.id, // Use the first ID found
          garmentCode: req.garmentCode,
          color: req.color,
          size: req.size,
          category: req.category,
          requiredQuantity: req.requiredQuantity,
          createdAt: req.createdAt,
          updatedAt: req.updatedAt,
          // Include transactions for modal details
          transactions: req.transactions || [],
          // Don't include shop since we're aggregating across shops
        });
      }
    }

    // Convert map back to array and filter out zero quantities
    return Array.from(aggregationMap.values())
      .filter(req => req.requiredQuantity > 0)
      .sort((a, b) => {
        // Sort by category, then garment code, then color, then size
        if (a.category !== b.category) return a.category.localeCompare(b.category);
        if (a.garmentCode !== b.garmentCode) return a.garmentCode.localeCompare(b.garmentCode);
        if (a.color !== b.color) return a.color.localeCompare(b.color);
        return a.size.localeCompare(b.size);
      });
  }

  /**
   * Extract shop identifier from order
   * @param {object} order - Shopify order object
   * @returns {string} - Shop identifier
   */
  extractShopFromOrder(order) {
    // Handle bulk order IDs that contain shop information
    if (order.id && typeof order.id === 'string' && order.id.includes('bulk_')) {
      // Extract shop from bulk order ID format: bulk_timestamp_shopidentifier
      const parts = order.id.split('_');
      if (parts.length >= 3) {
        // The shop identifier is typically the last part
        const shopPart = parts[parts.length - 1];
        // Convert shop identifier back to domain format if needed
        return this.normalizeShopIdentifier(shopPart);
      }
    }

    // Standard shop extraction
    return order.shop || order.shop_domain || 'unknown';
  }

  /**
   * Normalize shop identifier to proper domain format
   * @param {string} shopIdentifier - Shop identifier
   * @returns {string} - Normalized shop domain
   */
  normalizeShopIdentifier(shopIdentifier) {
    // If it's already a domain, return as-is
    if (shopIdentifier.includes('.myshopify.com')) {
      return shopIdentifier;
    }

    // Map known shop identifiers to domains
    const shopMappings = {
      'clnbg6wc9': 'stylish-stitches-jordin-kolman.myshopify.com',
      'xm69jjsdi': 'stylish-stitches-jordin-kolman.myshopify.com',
      'op6zroggc': 'stylish-stitches-jordin-kolman.myshopify.com',
      'im74efne3': 'stylish-stitches-jordin-kolman.myshopify.com',
      '64e3818su': 'stylish-stitches-jordin-kolman.myshopify.com'
      // Add more mappings as needed
    };

    return shopMappings[shopIdentifier] || shopIdentifier;
  }

  /**
   * Process bulk orders for inventory (used during initial bulk query)
   * @param {Array} orders - Array of Shopify orders
   * @param {string} shop - Shop identifier
   * @returns {Promise<object>} - Bulk processing results
   */
  async processBulkOrdersForInventory(orders, shop) {
    const results = {
      totalOrders: orders.length,
      processedOrders: 0,
      skippedOrders: 0,
      totalLineItems: 0,
      processedLineItems: 0,
      skippedLineItems: 0,
      errors: []
    };

    for (const order of orders) {
      try {
        // Determine transaction type based on fulfillment status
        const transactionType = this.determineTransactionTypeFromOrder(order);

        const orderResult = await this.processOrderForInventory(order, transactionType);

        results.processedOrders++;
        results.totalLineItems += (order.line_items || []).length;
        results.processedLineItems += orderResult.processed;
        results.skippedLineItems += orderResult.skipped;

        if (orderResult.errors.length > 0) {
          results.errors.push(...orderResult.errors);
        }
      } catch (error) {
        results.skippedOrders++;
        results.errors.push({
          orderId: order.id,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Determine transaction type from order fulfillment status
   * @param {object} order - Shopify order object
   * @returns {string} - Transaction type
   */
  determineTransactionTypeFromOrder(order) {
    // If order is fulfilled, we need to account for both creation and fulfillment
    // For bulk processing, we'll handle this by processing unfulfilled quantities only
    if (order.fulfillment_status === 'fulfilled') {
      return 'order_created'; // We'll handle fulfillments separately
    }

    if (order.cancelled_at) {
      return 'order_cancelled';
    }

    return 'order_created';
  }

  /**
   * Clean up inventory requirements with zero quantity
   * @param {string} shop - Shop identifier (optional)
   * @returns {Promise<number>} - Number of records cleaned up
   */
  async cleanupZeroQuantityRequirements(shop = null) {
    await this.ensureInitialized();

    const where = { requiredQuantity: { lte: 0 } };
    if (shop) {
      where.shop = shop;
    }

    const result = await this.prisma.inventoryRequirement.deleteMany({ where });
    return result.count;
  }

  /**
   * Mark specific inventory requirements as ordered (reset to 0)
   * @param {Array} selections - Array of {garmentCode, color, size, category} objects
   * @returns {Promise<object>} - Results of the operation
   */
  async markInventoryAsOrdered(selections) {
    try {
      await this.ensureInitialized();

      const results = {
        updated: 0,
        deleted: 0,
        transactionsDeleted: 0,
        errors: []
      };

      for (const selection of selections) {
        try {
          const { garmentCode, color, size, category } = selection;

          // Find all inventory requirements matching this selection across all shops
          const requirements = await this.prisma.inventoryRequirement.findMany({
            where: {
              garmentCode,
              color,
              size,
              category
            },
            include: {
              transactions: true
            }
          });

          for (const requirement of requirements) {
            // Delete all transactions for this requirement
            const deletedTransactions = await this.prisma.inventoryTransaction.deleteMany({
              where: {
                inventoryRequirementId: requirement.id
              }
            });

            results.transactionsDeleted += deletedTransactions.count;

            // Delete the inventory requirement itself
            await this.prisma.inventoryRequirement.delete({
              where: {
                id: requirement.id
              }
            });

            results.deleted++;
          }
        } catch (error) {
          results.errors.push({
            selection,
            error: error.message
          });
        }
      }

      return results;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to mark inventory as ordered: ${error.message}`,
        { selections }
      );
    }
  }

  /**
   * Mark all inventory requirements as ordered (reset everything to 0)
   * @param {string} category - Category to reset ('regular' or 'engraving', or null for all)
   * @returns {Promise<object>} - Results of the operation
   */
  async markAllInventoryAsOrdered(category = null) {
    try {
      await this.ensureInitialized();

      const where = {};
      if (category) {
        where.category = category;
      }

      // Get all inventory requirements to be deleted
      const requirements = await this.prisma.inventoryRequirement.findMany({
        where,
        include: {
          transactions: true
        }
      });

      const results = {
        deleted: 0,
        transactionsDeleted: 0,
        errors: []
      };

      // Delete all transactions first (due to foreign key constraints)
      const deletedTransactions = await this.prisma.inventoryTransaction.deleteMany({
        where: {
          inventoryRequirement: where
        }
      });

      results.transactionsDeleted = deletedTransactions.count;

      // Delete all inventory requirements
      const deletedRequirements = await this.prisma.inventoryRequirement.deleteMany({
        where
      });

      results.deleted = deletedRequirements.count;

      return results;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to mark all inventory as ordered: ${error.message}`,
        { category }
      );
    }
  }
}
