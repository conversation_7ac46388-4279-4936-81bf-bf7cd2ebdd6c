/**
 * Transaction Processing Service
 *
 * This service handles transaction retry logic and processing
 * of unprocessable items with safe balance updates.
 */

import { container } from '../../lib/container/ServiceContainer.server.js';
import { TransactionManager } from '../../lib/database/TransactionManager.js';
import { BusinessLogicError, ValidationError } from '../../lib/errors/AppError.js';
import { normalizeShopDomain } from '../../utils/id-normalization.server.js';

/**
 * Service for transaction processing and retry logic
 */
export class TransactionProcessingService {
  constructor(dependencies = {}) {
    this.prisma = dependencies.prisma || container.resolve('database');
    this.transactionManager = dependencies.transactionManager || new TransactionManager(this.prisma);
    this.orderProcessingService = dependencies.orderProcessingService || container.resolve('orderProcessingService');
    this.pricingService = dependencies.pricingService || container.resolve('pricingService');
  }

  /**
   * Safely updates a balance in a transaction-like manner
   * @param {string} shop - The shop identifier
   * @param {string} category - The product category
   * @param {number} quantity - The quantity to add
   * @param {number} month - The month (0-11)
   * @param {number} year - The year (e.g., 2025)
   * @returns {Promise<boolean>} - Whether the balance was successfully updated
   */
  async safelyUpdateBalance(shop, category, quantity, month, year) {
    try {
      // Validate inputs
      if (!shop || !category) {
        console.error(`Invalid shop or category - shop: ${shop}, category: ${category}`);
        return false;
      }

      if (typeof quantity !== 'number' || isNaN(quantity)) {
        console.error(`Invalid quantity: ${quantity}`);
        return false;
      }

      if (typeof month !== 'number' || month < 0 || month > 11) {
        console.error(`Invalid month: ${month}`);
        return false;
      }

      if (typeof year !== 'number' || year < 2020 || year > 2030) {
        console.error(`Invalid year: ${year}`);
        return false;
      }

      const numQuantity = Number(quantity);
      const normalizedShop = normalizeShopDomain(shop);

      // Get the price for this category
      const price = await this.pricingService.getPriceByCategory(category, normalizedShop);

      if (!price) {
        console.error(`No price found for category: ${category} in shop: ${normalizedShop}`);
        return false;
      }

      // Execute balance update in transaction
      const result = await this.transactionManager.executeTransaction(async (tx) => {
        // Check if balance exists
        let balance = await tx.invoiceBalance.findFirst({
          where: {
            shop: normalizedShop,
            category: category,
            month: month,
            year: year,
          },
        });

        if (!balance) {
          // Create new balance
          balance = await tx.invoiceBalance.create({
            data: {
              shop: normalizedShop,
              month: month,
              year: year,
              category: category,
              quantity: numQuantity,
              balance: Number(price.cost) * numQuantity,
            },
          });
        } else {
          // Update existing balance
          const newQuantity = Number(balance.quantity) + numQuantity;
          const newTotal = Number(balance.balance) + (Number(price.cost) * numQuantity);

          balance = await tx.invoiceBalance.update({
            where: { id: balance.id },
            data: {
              quantity: newQuantity,
              balance: newTotal,
            },
          });
        }

        return balance;
      });

      console.log(`Successfully updated balance for ${category}: ${result.quantity} units, $${result.balance}`);
      return true;

    } catch (error) {
      console.error(`Error in safelyUpdateBalance: ${error.message}`);
      return false;
    }
  }

  /**
   * Process unprocessable items and retry them
   * @param {string} shopDomain - Shop domain
   * @param {object} options - Processing options
   * @returns {Promise<object>} - Processing results
   */
  async processUnprocessableItems(shopDomain, options = {}) {
    try {
      const {
        limit = 50,
        progressCallback = null
      } = options;

      const normalizedShop = normalizeShopDomain(shopDomain);

      // Get unprocessable items
      const unprocessables = await this.prisma.unprocessable.findMany({
        where: {
          shop: normalizedShop,
        },
        take: limit,
        orderBy: {
          date: 'asc',
        },
      });

      if (unprocessables.length === 0) {
        return {
          processed: 0,
          successful: 0,
          failed: 0,
          results: []
        };
      }

      const results = [];
      let successful = 0;
      let failed = 0;

      for (let i = 0; i < unprocessables.length; i++) {
        const item = unprocessables[i];

        try {
          if (progressCallback) {
            progressCallback(`Processing unprocessable item ${i + 1}/${unprocessables.length}`);
          }

          const { productType, sku, variant, quantity, date, variantId, productId } = item;

          console.log(`[processUnprocessableItems] Retrying unprocessable item ${item.id}:`, {
            productType,
            sku,
            variant,
            quantity,
            variantId,
            productId,
            errorField: item.errorField,
            message: item.message
          });

          // Create a fresh line item with updated product data
          let lineItem = {
            id: variantId || `retry_${item.id}`,
            title: variant || '',
            quantity: quantity || 1,
            variant_id: variantId,
            sku: sku || '',
            product_id: productId,
            product_type: productType || '',
            variant_title: variant || '',
            price: '0.00',
            total_discount: '0.00',
            fulfillment_service: 'manual',
            requires_shipping: true,
            taxable: true,
            grams: 0,
            vendor: '',
            properties: [],
          };

          // If we have variant/product IDs, try to fetch fresh product data
          if (variantId || productId) {
            try {
              const shopifyAdmin = await this.shopifyService.getShopifyAdmin(normalizedShop);

              // Try to fetch updated product data
              if (variantId) {
                const variantQuery = `
                  query getVariant($id: ID!) {
                    productVariant(id: $id) {
                      id
                      title
                      sku
                      product {
                        id
                        title
                        productType
                        vendor
                      }
                    }
                  }
                `;

                const variantResponse = await shopifyAdmin.graphql(variantQuery, {
                  variables: { id: `gid://shopify/ProductVariant/${variantId}` }
                });

                const variantData = variantResponse.body?.data?.productVariant;
                if (variantData) {
                  // Update line item with fresh data
                  lineItem.sku = variantData.sku || lineItem.sku;
                  lineItem.variant_title = variantData.title || lineItem.variant_title;
                  lineItem.title = variantData.title || lineItem.title;
                  if (variantData.product) {
                    lineItem.product_type = variantData.product.productType || lineItem.product_type;
                    lineItem.vendor = variantData.product.vendor || lineItem.vendor;
                  }
                  console.log(`Updated product data for item ${item.id}:`, {
                    sku: lineItem.sku,
                    productType: lineItem.product_type,
                    vendor: lineItem.vendor
                  });
                }
              }
            } catch (fetchError) {
              console.log(`Could not fetch updated product data for item ${item.id}: ${fetchError.message}`);
              // Continue with stored data
            }
          }

          // Create processing context
          const context = {
            shop: normalizedShop,
            createdAt: date || new Date(),
            orderId: `retry_${item.id}_${Date.now()}`
          };

          // Try to process the line item directly using ProductProcessor
          try {
            const result = await this.productProcessor.processLineItem(lineItem, context);

            if (result && result.category) {
              // Success! Add to invoice balance
              const itemDate = date || new Date();
              const month = itemDate.getMonth();
              const year = itemDate.getFullYear();

              await this.safelyUpdateBalance(
                normalizedShop,
                result.category,
                quantity,
                month,
                year
              );

              // Delete the unprocessable item
              await this.prisma.unprocessable.delete({
                where: { id: item.id }
              });

              console.log(`Successfully reprocessed unprocessable item ${item.id} as category '${result.category}'`);
              successful++;
              results.push({
                id: item.id,
                success: true,
                category: result.category,
                quantity: quantity
              });
            } else {
              // Processing didn't return expected result
              failed++;
              results.push({
                id: item.id,
                success: false,
                error: 'Processing completed but no category determined'
              });
            }
          } catch (processingError) {
            // Processing failed - leave the unprocessable item as-is
            console.log(`Processing still failed for item ${item.id}: ${processingError.message}`);
            failed++;
            results.push({
              id: item.id,
              success: false,
              error: processingError.message
            });
          }

        } catch (error) {
          console.error(`Error processing unprocessable item ${item.id}: ${error.message}`);
          failed++;
          results.push({
            id: item.id,
            success: false,
            error: error.message
          });
        }
      }

      return {
        processed: unprocessables.length,
        successful,
        failed,
        results
      };

    } catch (error) {
      throw new BusinessLogicError(
        `Failed to process unprocessable items: ${error.message}`,
        { shopDomain }
      );
    }
  }

  /**
   * Retry specific unprocessable item by variant ID
   * @param {string} shopDomain - Shop domain
   * @param {string} variantId - Variant ID to retry
   * @param {string} productId - Product ID
   * @param {string} productType - Product type
   * @returns {Promise<boolean>} - Whether retry was successful
   */
  async retrySpecificUnprocessable(shopDomain, variantId, productId, productType) {
    try {
      const normalizedShop = normalizeShopDomain(shopDomain);

      // Find unprocessable items matching the variant
      const unprocessables = await this.prisma.unprocessable.findMany({
        where: {
          shop: normalizedShop,
          variantId: variantId?.toString()
        }
      });

      if (unprocessables.length === 0) {
        return false;
      }

      let anySuccessful = false;

      for (const item of unprocessables) {
        try {
          const { orderId } = item;

          if (!orderId) {
            console.error(`Missing orderId in unprocessable item: ${item.id}`);
            continue;
          }

          // Try to reprocess the order
          const processingResult = await this.orderProcessingService.processOrder(
            orderId,
            normalizedShop,
            {
              skipValidation: true,
              forceReprocess: true,
              dryRun: false
            }
          );

          if (processingResult.success) {
            // Delete the unprocessable item
            await this.prisma.unprocessable.delete({
              where: { id: item.id }
            });
            anySuccessful = true;
          }

        } catch (error) {
          console.error(`Error retrying unprocessable item ${item.id}: ${error.message}`);
        }
      }

      return anySuccessful;

    } catch (error) {
      console.error(`Error in retrySpecificUnprocessable: ${error.message}`);
      return false;
    }
  }



  /**
   * Get unprocessable items statistics
   * @param {string} shopDomain - Shop domain (optional)
   * @returns {Promise<object>} - Statistics
   */
  async getUnprocessableStatistics(shopDomain = null) {
    try {
      const whereClause = shopDomain ? { shop: normalizeShopDomain(shopDomain) } : {};

      const [total, byShop, recent] = await Promise.all([
        this.prisma.unprocessable.count({ where: whereClause }),
        this.prisma.unprocessable.groupBy({
          by: ['shop'],
          _count: true,
          where: whereClause,
        }),
        this.prisma.unprocessable.count({
          where: {
            ...whereClause,
            date: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
            },
          },
        }),
      ]);

      return {
        total,
        byShop: byShop.reduce((acc, item) => {
          acc[item.shop] = item._count;
          return acc;
        }, {}),
        recent,
      };

    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get unprocessable statistics: ${error.message}`,
        { shopDomain }
      );
    }
  }
}
