/**
 * Transaction Processing Service
 *
 * This service handles transaction retry logic and processing
 * of unprocessable items with safe balance updates.
 */

import { container } from '../../lib/container/ServiceContainer.server.js';
import { TransactionManager } from '../../lib/database/TransactionManager.js';
import { BusinessLogicError, ValidationError } from '../../lib/errors/AppError.js';
import { normalizeShopDomain } from '../../utils/id-normalization.server.js';

/**
 * Service for transaction processing and retry logic
 */
export class TransactionProcessingService {
  constructor(dependencies = {}) {
    this.prisma = dependencies.prisma || container.resolve('database');
    this.transactionManager = dependencies.transactionManager || new TransactionManager(this.prisma);
    this.orderProcessingService = dependencies.orderProcessingService || container.resolve('orderProcessingService');
    this.pricingService = dependencies.pricingService || container.resolve('pricingService');
  }

  /**
   * Safely updates a balance in a transaction-like manner
   * @param {string} shop - The shop identifier
   * @param {string} category - The product category
   * @param {number} quantity - The quantity to add
   * @param {number} month - The month (0-11)
   * @param {number} year - The year (e.g., 2025)
   * @returns {Promise<boolean>} - Whether the balance was successfully updated
   */
  async safelyUpdateBalance(shop, category, quantity, month, year) {
    try {
      // Validate inputs
      if (!shop || !category) {
        console.error(`Invalid shop or category - shop: ${shop}, category: ${category}`);
        return false;
      }

      if (typeof quantity !== 'number' || isNaN(quantity)) {
        console.error(`Invalid quantity: ${quantity}`);
        return false;
      }

      if (typeof month !== 'number' || month < 0 || month > 11) {
        console.error(`Invalid month: ${month}`);
        return false;
      }

      if (typeof year !== 'number' || year < 2020 || year > 2030) {
        console.error(`Invalid year: ${year}`);
        return false;
      }

      const numQuantity = Number(quantity);
      const normalizedShop = normalizeShopDomain(shop);

      // Get the price for this category
      const price = await this.pricingService.getPriceByCategory(category, normalizedShop);

      if (!price) {
        console.error(`No price found for category: ${category} in shop: ${normalizedShop}`);
        return false;
      }

      // Execute balance update in transaction
      const result = await this.transactionManager.executeTransaction(async (tx) => {
        // Check if balance exists
        let balance = await tx.invoiceBalance.findFirst({
          where: {
            shop: normalizedShop,
            category: category,
            month: month,
            year: year,
          },
        });

        if (!balance) {
          // Create new balance
          balance = await tx.invoiceBalance.create({
            data: {
              shop: normalizedShop,
              month: month,
              year: year,
              category: category,
              quantity: numQuantity,
              balance: Number(price.cost) * numQuantity,
            },
          });
        } else {
          // Update existing balance
          const newQuantity = Number(balance.quantity) + numQuantity;
          const newTotal = Number(balance.balance) + (Number(price.cost) * numQuantity);

          balance = await tx.invoiceBalance.update({
            where: { id: balance.id },
            data: {
              quantity: newQuantity,
              balance: newTotal,
            },
          });
        }

        return balance;
      });

      console.log(`Successfully updated balance for ${category}: ${result.quantity} units, $${result.balance}`);
      return true;

    } catch (error) {
      console.error(`Error in safelyUpdateBalance: ${error.message}`);
      return false;
    }
  }

  /**
   * Process unprocessable items and retry them
   * @param {string} shopDomain - Shop domain
   * @param {object} options - Processing options
   * @returns {Promise<object>} - Processing results
   */
  async processUnprocessableItems(shopDomain, options = {}) {
    try {
      const {
        limit = 50,
        progressCallback = null
      } = options;

      const normalizedShop = normalizeShopDomain(shopDomain);

      // Get unprocessable items
      const unprocessables = await this.prisma.unprocessable.findMany({
        where: {
          shop: normalizedShop,
        },
        take: limit,
        orderBy: {
          date: 'asc',
        },
      });

      if (unprocessables.length === 0) {
        return {
          processed: 0,
          successful: 0,
          failed: 0,
          results: []
        };
      }

      const results = [];
      let successful = 0;
      let failed = 0;

      for (let i = 0; i < unprocessables.length; i++) {
        const item = unprocessables[i];

        try {
          if (progressCallback) {
            progressCallback(`Processing unprocessable item ${i + 1}/${unprocessables.length}`);
          }

          // Extract information directly from the unprocessable item fields
          const {
            productType,
            variant,
            sku,
            quantity = 1,
            date
          } = item;

          console.log(`Retrying unprocessable item ${item.id}: productType=${productType}, sku=${sku}, quantity=${quantity}`);

          // Try to determine the category using the same logic as ProductProcessor
          let category;
          try {
            // If productType is missing or empty, process by SKU like the original logic
            if (!productType || productType.trim() === '') {
              category = this.processBySku(sku || '', variant || '');
            } else {
              category = this.determineProductCategory(productType, sku || '');
            }
          } catch (categoryError) {
            console.error(`Could not determine category for unprocessable item ${item.id}: ${categoryError.message}`);
            failed++;
            results.push({
              id: item.id,
              success: false,
              error: `Could not determine category: ${categoryError.message}`
            });
            continue;
          }

          if (!category) {
            console.error(`No category found for unprocessable item ${item.id} with productType: ${productType}`);
            failed++;
            results.push({
              id: item.id,
              success: false,
              error: 'No category found for product type'
            });
            continue;
          }

          // Get the date for month/year calculation
          const itemDate = date || new Date();
          const month = itemDate.getMonth();
          const year = itemDate.getFullYear();

          console.log(`Attempting to add ${quantity} units of category '${category}' for ${month}/${year}`);

          // Try to update the balance directly
          const balanceUpdated = await this.safelyUpdateBalance(
            normalizedShop,
            category,
            quantity,
            month,
            year
          );

          if (balanceUpdated) {
            // Delete the unprocessable item since it was successfully processed
            await this.prisma.unprocessable.delete({
              where: { id: item.id }
            });

            console.log(`Successfully processed unprocessable item ${item.id} as category '${category}'`);
            successful++;
            results.push({
              id: item.id,
              success: true,
              category: category,
              quantity: quantity
            });
          } else {
            console.error(`Failed to update balance for unprocessable item ${item.id}`);
            failed++;
            results.push({
              id: item.id,
              success: false,
              error: 'Failed to update balance'
            });
          }

        } catch (error) {
          console.error(`Error processing unprocessable item ${item.id}: ${error.message}`);
          failed++;
          results.push({
            id: item.id,
            success: false,
            error: error.message
          });
        }
      }

      return {
        processed: unprocessables.length,
        successful,
        failed,
        results
      };

    } catch (error) {
      throw new BusinessLogicError(
        `Failed to process unprocessable items: ${error.message}`,
        { shopDomain }
      );
    }
  }

  /**
   * Retry specific unprocessable item by variant ID
   * @param {string} shopDomain - Shop domain
   * @param {string} variantId - Variant ID to retry
   * @param {string} productId - Product ID
   * @param {string} productType - Product type
   * @returns {Promise<boolean>} - Whether retry was successful
   */
  async retrySpecificUnprocessable(shopDomain, variantId, productId, productType) {
    try {
      const normalizedShop = normalizeShopDomain(shopDomain);

      // Find unprocessable items matching the variant
      const unprocessables = await this.prisma.unprocessable.findMany({
        where: {
          shop: normalizedShop,
          data: {
            contains: variantId
          }
        }
      });

      if (unprocessables.length === 0) {
        return false;
      }

      let anySuccessful = false;

      for (const item of unprocessables) {
        try {
          // Parse the data
          let itemData;
          try {
            itemData = typeof item.data === 'string' ? JSON.parse(item.data) : item.data;
          } catch (parseError) {
            console.error(`Failed to parse unprocessable item data: ${parseError.message}`);
            continue;
          }

          // Update the product type if provided
          if (productType) {
            itemData.productType = productType;
          }

          // Try to reprocess using the updated product type
          if (itemData.orderId) {
            const processingResult = await this.orderProcessingService.processOrder(
              itemData.orderId,
              normalizedShop,
              {
                skipValidation: true,
                forceReprocess: true,
                dryRun: false
              }
            );

            if (processingResult.success) {
              // Delete the unprocessable item
              await this.prisma.unprocessable.delete({
                where: { id: item.id }
              });
              anySuccessful = true;
            }
          }

        } catch (error) {
          console.error(`Error retrying unprocessable item ${item.id}: ${error.message}`);
        }
      }

      return anySuccessful;

    } catch (error) {
      console.error(`Error in retrySpecificUnprocessable: ${error.message}`);
      return false;
    }
  }

  /**
   * Determine product category based on product type and SKU
   * @param {string} productType - Product type
   * @param {string} sku - Product SKU
   * @returns {string} - Determined category
   */
  determineProductCategory(productType, sku = '') {
    if (!productType) {
      throw new Error('Product type is required');
    }

    const normalizedType = productType.toLowerCase().trim();
    const normalizedSku = sku.toLowerCase().trim();

    // Remove "private" prefixes
    const cleanType = normalizedType.replace(/^(private\s+)+/g, '');

    // Map specific product types to categories
    const typeMap = {
      'performance polo': 'polo',
      'performance gear': 'performance wear',
      'performance long sleeve': 'performance wear',
      'long sleeve shirt': 'long sleeve',
      'windbreaker': 'outerwear',
      'tumbler': 'tumbler',
      'headwear': 'hat',
      'work shirt': 'work shirt',
      'hoodie': 'hoodie',
      'sweatshirt': 'hoodie',
      'sweater': 'hoodie',
      'crewneck': 'hoodie',
      'woobie': 'woobie',
      'shirt': 'shirt',
      'tee': 'shirt',
      't-shirt': 'shirt',
      'polo': 'polo',
      'hat': 'hat',
      'cap': 'hat',
      'beanie': 'hat',
      'flag': 'flag',
      'patch': 'patch',
      'sticker': 'sticker',
      'decal': 'sticker',
      'canvas': 'canvas',
      'print': 'canvas'
    };

    // Check for direct matches first
    if (typeMap[cleanType]) {
      return typeMap[cleanType];
    }

    // Check for partial matches
    for (const [key, value] of Object.entries(typeMap)) {
      if (cleanType.includes(key)) {
        return value;
      }
    }

    // Check SKU for additional clues
    if (normalizedSku.startsWith('f-') || normalizedSku.includes('flag')) {
      return 'flag';
    }
    if (normalizedSku.includes('patch')) {
      return 'patch';
    }
    if (normalizedSku.includes('sticker')) {
      return 'sticker';
    }

    // Default fallback - use the cleaned product type as category
    return cleanType || 'other';
  }

  /**
   * Get unprocessable items statistics
   * @param {string} shopDomain - Shop domain (optional)
   * @returns {Promise<object>} - Statistics
   */
  async getUnprocessableStatistics(shopDomain = null) {
    try {
      const whereClause = shopDomain ? { shop: normalizeShopDomain(shopDomain) } : {};

      const [total, byShop, recent] = await Promise.all([
        this.prisma.unprocessable.count({ where: whereClause }),
        this.prisma.unprocessable.groupBy({
          by: ['shop'],
          _count: true,
          where: whereClause,
        }),
        this.prisma.unprocessable.count({
          where: {
            ...whereClause,
            date: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
            },
          },
        }),
      ]);

      return {
        total,
        byShop: byShop.reduce((acc, item) => {
          acc[item.shop] = item._count;
          return acc;
        }, {}),
        recent,
      };

    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get unprocessable statistics: ${error.message}`,
        { shopDomain }
      );
    }
  }
}
