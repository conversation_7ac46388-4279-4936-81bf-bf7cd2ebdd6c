-- CreateTable
CREATE TABLE "InventoryRequirement" (
    "id" SERIAL NOT NULL,
    "shop" TEXT NOT NULL,
    "garmentCode" TEXT NOT NULL,
    "color" TEXT NOT NULL,
    "size" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "requiredQuantity" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InventoryRequirement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InventoryTransaction" (
    "id" SERIAL NOT NULL,
    "inventoryRequirementId" INTEGER NOT NULL,
    "orderId" TEXT NOT NULL,
    "lineItemId" TEXT,
    "quantityChange" INTEGER NOT NULL,
    "transactionType" TEXT NOT NULL,
    "sku" TEXT,
    "productTitle" TEXT,
    "variantTitle" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "metadata" TEXT,

    CONSTRAINT "InventoryTransaction_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "InventoryRequirement_shop_idx" ON "InventoryRequirement"("shop");

-- CreateIndex
CREATE INDEX "InventoryRequirement_category_idx" ON "InventoryRequirement"("category");

-- CreateIndex
CREATE INDEX "InventoryRequirement_garmentCode_idx" ON "InventoryRequirement"("garmentCode");

-- CreateIndex
CREATE INDEX "InventoryRequirement_shop_category_idx" ON "InventoryRequirement"("shop", "category");

-- CreateIndex
CREATE UNIQUE INDEX "InventoryRequirement_shop_garmentCode_color_size_category_key" ON "InventoryRequirement"("shop", "garmentCode", "color", "size", "category");

-- CreateIndex
CREATE INDEX "InventoryTransaction_inventoryRequirementId_idx" ON "InventoryTransaction"("inventoryRequirementId");

-- CreateIndex
CREATE INDEX "InventoryTransaction_orderId_idx" ON "InventoryTransaction"("orderId");

-- CreateIndex
CREATE INDEX "InventoryTransaction_transactionType_idx" ON "InventoryTransaction"("transactionType");

-- CreateIndex
CREATE INDEX "InventoryTransaction_createdAt_idx" ON "InventoryTransaction"("createdAt");

-- AddForeignKey
ALTER TABLE "InventoryTransaction" ADD CONSTRAINT "InventoryTransaction_inventoryRequirementId_fkey" FOREIGN KEY ("inventoryRequirementId") REFERENCES "InventoryRequirement"("id") ON DELETE CASCADE ON UPDATE CASCADE;
